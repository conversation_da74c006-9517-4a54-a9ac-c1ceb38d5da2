// src/app/dashboard/layout.tsx
'use client';

import { useEffect, useState } from 'react';
import { requireAuth } from '../../lib/auth';
import DashboardContent from './_components/dashboard-content';
import DashboardHeader from './_components/dashboard-header';
import DashboardSidebar from './_components/dashboard-sidebar';
import {
    AcademicManagement,
    AttendanceManagement,
    FeeManagement,
    LeaveManagement,
    LibraryManagement,
    ProfilePage,
    Reports,
    StaffManagement,
    StudentManagement,
    TransportManagement
} from './_components/section-components';

export default function DashboardLayout({
  children: _children,
}: {
  children: React.ReactNode;
}) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState('Dashboard');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Pages that should open in full-page mode by default (sidebar hidden)
  const fullPageSections = ['Profile'];

  useEffect(() => {
    const checkAuth = async () => {
      const authResult = await requireAuth();
      setIsAuthenticated(authResult);
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const handleSectionChange = (section: string) => {
    setActiveSection(section);
    // If navigating to a full-page section, ensure sidebar is hidden
    if (fullPageSections.includes(section)) {
      setIsSidebarCollapsed(true);
    }
  };

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'Dashboard':
        return <DashboardContent />;
      case 'Student Management':
      case 'Current Students':
      case 'Enroll Student':
      case 'Class Assignment':
      case 'School Records':
        return <StudentManagement activeSubSection={activeSection} />;
      case 'Staff Management':
        return <StaffManagement />;
      case 'Attendance Management':
        return <AttendanceManagement />;
      case 'Leave Management':
        return <LeaveManagement />;
      case 'Academic Management':
        return <AcademicManagement />;
      case 'Fee Management':
        return <FeeManagement />;
      case 'Library Management':
        return <LibraryManagement />;
      case 'Transport Management':
        return <TransportManagement />;
      case 'Reports':
        return <Reports />;
      case 'Profile':
        return <ProfilePage />;
      default:
        return <DashboardContent />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // requireAuth will handle the redirect
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Sidebar - Fixed position */}
      <div className="flex-shrink-0 transition-all duration-300 relative">
        <DashboardSidebar
          activeItem={activeSection}
          onItemClick={handleSectionChange}
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={toggleSidebar}
        />
      </div>

      {/* Main Content Area with proper margin for fixed sidebar */}
      <div className={`flex flex-col min-h-screen transition-all duration-300 ${
        isSidebarCollapsed ? 'ml-14' : 'ml-64'
      }`}>
        {/* Elegant separation line */}
        <div className={`absolute top-0 bottom-0 w-0.5 bg-gradient-to-b from-gray-200 via-gray-300 to-gray-200 z-10 transition-all duration-300 ${
          isSidebarCollapsed ? 'left-14' : 'left-64'
        }`}></div>

        {/* Header - Fixed at top */}
        <div className="flex-shrink-0">
          <DashboardHeader
            title={activeSection}
            onNavigate={handleSectionChange}
          />
        </div>

        {/* Page Content - Scrollable */}
        <main className="flex-1 overflow-y-auto bg-gray-100 pl-2">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}
