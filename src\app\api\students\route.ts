// src/app/api/students/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { MasterDataService } from '../../../services/masterDataService';
import { CreateStudentData } from '../../../services/studentManagementService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const classId = searchParams.get('classId');
    const sectionId = searchParams.get('sectionId');
    const academicYearId = searchParams.get('academicYearId');

    let students: any[] = [];

    if (search) {
      // TODO: Implement search with new service
      students = [];
    } else if (classId && sectionId) {
      // TODO: Implement class/section filtering with new service
      students = [];
    } else {
      // TODO: Implement get all students with new service
      students = [];
    }

    return NextResponse.json({ 
      success: true, 
      data: students 
    });
  } catch (error) {
    console.error('Error in GET /api/students:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch students' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = [
      'firstName', 'lastName', 'dateOfBirth', 'gender', 'guardianName',
      'guardianRelationId', 'guardianPhone', 'classId', 'sectionId',
      'rollNumber', 'academicYearId'
    ];

    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Check if roll number already exists
    const rollNumberExists = await MasterDataService.isRollNumberExists(
      body.rollNumber,
      body.classId,
      body.sectionId,
      body.academicYearId
    );

    if (rollNumberExists) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Roll number already exists for this class, section, and academic year' 
        },
        { status: 409 }
      );
    }

    const studentData: CreateStudentData = {
      student: {
        first_name: body.firstName,
        last_name: body.lastName,
        email: body.email,
        phone_number: body.phoneNumber,
        date_of_birth: body.dateOfBirth,
        gender: body.gender,
        address: body.address,
        emergency_contact: body.emergencyContact,
        guardian_name: body.guardianName,
        guardian_relation_id: body.guardianRelationId,
        guardian_phone: body.guardianPhone,
        guardian_email: body.guardianEmail,
        guardian_address: body.guardianAddress,
        class_id: body.classId,
        section_id: body.sectionId,
        roll_number: body.rollNumber,
        academic_year_id: body.academicYearId,
        previous_school: body.previousSchool,
        is_active: true
      }
    };

    // TODO: Implement create student with new service
    console.log('Create student:', studentData);
    const student = { id: 'temp-id', ...studentData };

    return NextResponse.json(
      { success: true, data: student },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/students:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to create student' 
      },
      { status: 500 }
    );
  }
}
