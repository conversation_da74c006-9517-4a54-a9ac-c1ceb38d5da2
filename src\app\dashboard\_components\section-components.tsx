// src/app/dashboard/_components/section-components.tsx
'use client';

import PageWrapper from '../../../components/common/page-wrapper';
import StudentManagement from '../../../components/student-management/student-management';
import ProfilePage from '../../../components/profile/profile-page';

// Placeholder components for sections that will be implemented later
export const StaffManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Staff management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

export const AttendanceManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Attendance management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

export const LeaveManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Leave management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

export const AcademicManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Academic management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

export const FeeManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Fee management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

export const LibraryManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Library management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

export const TransportManagement = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Transport management features will be implemented here.</p>
    </div>
  </PageWrapper>
);

export const Reports = () => (
  <PageWrapper>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-300">
      <p className="text-gray-600 text-sm">Reports and analytics will be implemented here.</p>
    </div>
  </PageWrapper>
);

// Re-export existing components for consistency
export { StudentManagement, ProfilePage };
