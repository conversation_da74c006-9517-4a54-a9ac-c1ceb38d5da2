// src/services/studentManagementService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database';
import { <PERSON><PERSON>r<PERSON><PERSON>, <PERSON>rror<PERSON>andler, ServiceError } from './core/errorHandler';

// Repository imports
import { AcademicRecordRepository, IAcademicRecordRepository } from '../repositories/academicRecordRepository';
import { GuardianRepository, IGuardianRepository } from '../repositories/guardianRepository';
import { IStudentRepository, StudentRepository } from '../repositories/studentRepository';

// Type imports
import {
    AcademicRecordEntity,
    GuardianEntity,
    GuardianInsert,
    StudentEntity,
    StudentInsert,
    StudentUpdate
} from '../constants/database';
import { PaginatedResult, PaginationOptions } from '../repositories/baseRepository';

/**
 * Complete student data interface for enrollment
 */
export interface CompleteStudentData {
  student: StudentEntity;
  guardians: GuardianEntity[];
  academicRecord: AcademicRecordEntity;
}

/**
 * Student creation data interface (simplified for current architecture)
 */
export interface CreateStudentData {
  student: StudentInsert;
}

/**
 * Student with relations interface (for backward compatibility)
 */
export interface StudentWithRelations extends StudentEntity {
  class: { id: string; name: string } | null;
  section: { id: string; name: string } | null;
  academic_year: { id: string; year: string } | null;
  guardian_relation: { id: string; name: string } | null;
}

/**
 * Student search filters interface
 */
export interface StudentSearchFilters {
  searchTerm?: string;
  classId?: string;
  sectionId?: string;
  academicYearId?: string;
  isActive?: boolean;
}

/**
 * Student management service interface
 */
export interface IStudentManagementService {
  // Core CRUD operations
  createCompleteStudent(data: CreateStudentData): Promise<CompleteStudentData>;
  getStudentById(id: string): Promise<CompleteStudentData | null>;
  updateStudent(id: string, data: StudentUpdate): Promise<StudentEntity>;
  deleteStudent(id: string): Promise<void>;
  
  // Search and listing
  searchStudents(filters: StudentSearchFilters, pagination?: PaginationOptions): Promise<PaginatedResult<StudentEntity>>;
  getStudentsByClass(classId: string, sectionId: string, academicYearId: string, pagination?: PaginationOptions): Promise<PaginatedResult<CompleteStudentData>>;
  
  // Guardian management
  addGuardian(studentId: string, guardianData: GuardianInsert): Promise<GuardianEntity>;
  updateGuardian(guardianId: string, data: Partial<GuardianInsert>): Promise<GuardianEntity>;
  removeGuardian(guardianId: string): Promise<void>;
  setPrimaryGuardian(guardianId: string, studentId: string): Promise<void>;
  
  // Validation and business rules
  validateRollNumberUniqueness(rollNumber: string, classId: string, sectionId: string, academicYearId: string, excludeStudentId?: string): Promise<boolean>;
  checkClassCapacity(classId: string, sectionId: string, academicYearId: string): Promise<{ current: number; maximum: number; available: number }>;
  
  // Bulk operations
  transferStudents(studentIds: string[], newClassId: string, newSectionId: string): Promise<void>;
  promoteStudents(classId: string, sectionId: string, currentAcademicYearId: string, newAcademicYearId: string): Promise<void>;
}

/**
 * Student management service implementation
 * Orchestrates business logic and coordinates between multiple repositories
 */
export class StudentManagementService implements IStudentManagementService {
  private studentRepository: IStudentRepository;
  private guardianRepository: IGuardianRepository;
  private academicRecordRepository: IAcademicRecordRepository;

  constructor(client: SupabaseClient<Database>) {
    this.studentRepository = new StudentRepository(client);
    this.guardianRepository = new GuardianRepository(client);
    this.academicRecordRepository = new AcademicRecordRepository(client);
  }

  /**
   * Create a complete student record with guardian and academic information
   * @param data - Complete student creation data
   * @returns Complete student data
   */
  async createCompleteStudent(data: CreateStudentData): Promise<CompleteStudentData> {
    try {
      console.log('Creating complete student record', { 
        studentName: `${data.student.first_name} ${data.student.last_name}`,
        rollNumber: data.student.roll_number
      });

      // Validate business rules
      await this.validateStudentCreation(data);

      // Create student record
      const student = await this.studentRepository.create(data.student);

      // Create guardian record
      const guardianData = {
        name: data.student.guardian_name,
        phone: data.student.guardian_phone,
        email: data.student.guardian_email,
        relation_id: data.student.guardian_relation_id,
        address: data.student.guardian_address,
        student_id: student.id
      };
      const guardian = await this.guardianRepository.create(guardianData);

      // Create academic record
      const academicData = {
        class_id: data.student.class_id,
        section_id: data.student.section_id,
        roll_number: data.student.roll_number,
        academic_year_id: data.student.academic_year_id,
        admission_date: new Date().toISOString(),
        student_id: student.id
      };
      const academicRecord = await this.academicRecordRepository.create(academicData);

      const result = {
        student,
        guardians: [guardian],
        academicRecord
      };

      console.log('Complete student record created successfully', { 
        studentId: student.id,
        rollNumber: academicRecord.roll_number 
      });

      return result;
    } catch (error) {
      console.error('Error creating complete student record', { error, data });
      throw ErrorHandler.handle(error, 'Create complete student');
    }
  }

  /**
   * Get complete student data by ID
   * @param id - Student ID
   * @returns Complete student data or null
   */
  async getStudentById(id: string): Promise<CompleteStudentData | null> {
    try {
      console.log('Getting student by ID', { studentId: id });

      const student = await this.studentRepository.findById(id);
      if (!student) {
        return null;
      }

      const [guardians, academicRecord] = await Promise.all([
        this.guardianRepository.findByStudentId(id),
        this.academicRecordRepository.findByStudentId(id)
      ]);

      if (!academicRecord) {
        throw new ServiceError(
          ErrorCode.DATA_INTEGRITY_ERROR,
          'Student found but academic record is missing',
          null,
          'Get student by ID'
        );
      }

      const result = {
        student,
        guardians,
        academicRecord
      };

      console.log('Student data retrieved successfully', { 
        studentId: id,
        guardiansCount: guardians.length 
      });

      return result;
    } catch (error) {
      console.error('Error getting student by ID', { error, studentId: id });
      throw ErrorHandler.handle(error, 'Get student by ID');
    }
  }

  /**
   * Update student information
   * @param id - Student ID
   * @param data - Student update data
   * @returns Updated student entity
   */
  async updateStudent(id: string, data: StudentUpdate): Promise<StudentEntity> {
    try {
      console.log('Updating student', { studentId: id });

      const student = await this.studentRepository.update(id, data);

      console.log('Student updated successfully', { studentId: id });
      return student;
    } catch (error) {
      console.error('Error updating student', { error, studentId: id });
      throw ErrorHandler.handle(error, 'Update student');
    }
  }

  /**
   * Delete student and all related records
   * @param id - Student ID
   */
  async deleteStudent(id: string): Promise<void> {
    try {
      console.log('Deleting student', { studentId: id });

      // Soft delete related records first
      const guardians = await this.guardianRepository.findByStudentId(id);
      const academicRecord = await this.academicRecordRepository.findByStudentId(id);

      // Delete guardians
      for (const guardian of guardians) {
        await this.guardianRepository.softDelete(guardian.id);
      }

      // Delete academic record
      if (academicRecord) {
        await this.academicRecordRepository.softDelete(academicRecord.id);
      }

      // Finally delete student
      await this.studentRepository.softDelete(id);

      console.log('Student deleted successfully', { studentId: id });
    } catch (error) {
      console.error('Error deleting student', { error, studentId: id });
      throw ErrorHandler.handle(error, 'Delete student');
    }
  }

  /**
   * Search students with filters
   * @param filters - Search filters
   * @param pagination - Pagination options
   * @returns Paginated search results
   */
  async searchStudents(
    filters: StudentSearchFilters,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<StudentEntity>> {
    try {
      console.log('Searching students', { filters });

      if (filters.searchTerm) {
        return await this.studentRepository.searchStudents(filters.searchTerm, pagination);
      }

      // Build filter object for repository
      const repositoryFilters: Record<string, any> = {};
      
      if (filters.classId) repositoryFilters.class_id = filters.classId;
      if (filters.sectionId) repositoryFilters.section_id = filters.sectionId;
      if (filters.academicYearId) repositoryFilters.academic_year_id = filters.academicYearId;

      const result = await this.studentRepository.findAll(repositoryFilters, pagination);

      console.log('Students search completed', { 
        filters, 
        resultCount: result.data.length 
      });

      return result;
    } catch (error) {
      console.error('Error searching students', { error, filters });
      throw ErrorHandler.handle(error, 'Search students');
    }
  }

  /**
   * Get students by class with complete data
   * @param classId - Class ID
   * @param sectionId - Section ID
   * @param academicYearId - Academic year ID
   * @param pagination - Pagination options
   * @returns Paginated list of complete student data
   */
  async getStudentsByClass(
    classId: string,
    sectionId: string,
    academicYearId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<CompleteStudentData>> {
    try {
      console.log('Getting students by class', { classId, sectionId, academicYearId });

      const studentsResult = await this.studentRepository.findByClassAndSection(
        classId,
        sectionId,
        academicYearId,
        pagination
      );

      // Get complete data for each student
      const completeStudentsData = await Promise.all(
        studentsResult.data.map(async (student) => {
          const [guardians, academicRecord] = await Promise.all([
            this.guardianRepository.findByStudentId(student.id),
            this.academicRecordRepository.findByStudentId(student.id)
          ]);

          return {
            student,
            guardians,
            academicRecord: academicRecord!
          };
        })
      );

      const result = {
        data: completeStudentsData,
        pagination: studentsResult.pagination
      };

      console.log('Students by class retrieved successfully', { 
        classId, 
        sectionId, 
        academicYearId, 
        count: result.data.length 
      });

      return result;
    } catch (error) {
      console.error('Error getting students by class', { error, classId, sectionId });
      throw ErrorHandler.handle(error, 'Get students by class');
    }
  }

  /**
   * Add guardian to student
   * @param studentId - Student ID
   * @param guardianData - Guardian data
   * @returns Created guardian entity
   */
  async addGuardian(studentId: string, guardianData: GuardianInsert): Promise<GuardianEntity> {
    try {
      console.log('Adding guardian to student', { studentId, guardianName: guardianData.name });

      const data = {
        ...guardianData,
        student_id: studentId
      };

      const guardian = await this.guardianRepository.create(data);

      console.log('Guardian added successfully', { 
        guardianId: guardian.id, 
        studentId 
      });

      return guardian;
    } catch (error) {
      console.error('Error adding guardian', { error, studentId });
      throw ErrorHandler.handle(error, 'Add guardian');
    }
  }

  /**
   * Update guardian information
   * @param guardianId - Guardian ID
   * @param data - Guardian update data
   * @returns Updated guardian entity
   */
  async updateGuardian(guardianId: string, data: Partial<GuardianInsert>): Promise<GuardianEntity> {
    try {
      console.log('Updating guardian', { guardianId });

      const guardian = await this.guardianRepository.update(guardianId, data);

      console.log('Guardian updated successfully', { guardianId });
      return guardian;
    } catch (error) {
      console.error('Error updating guardian', { error, guardianId });
      throw ErrorHandler.handle(error, 'Update guardian');
    }
  }

  /**
   * Remove guardian from student
   * @param guardianId - Guardian ID
   */
  async removeGuardian(guardianId: string): Promise<void> {
    try {
      console.log('Removing guardian', { guardianId });

      await this.guardianRepository.softDelete(guardianId);

      console.log('Guardian removed successfully', { guardianId });
    } catch (error) {
      console.error('Error removing guardian', { error, guardianId });
      throw ErrorHandler.handle(error, 'Remove guardian');
    }
  }

  /**
   * Set primary guardian for student
   * @param guardianId - Guardian ID
   * @param studentId - Student ID
   */
  async setPrimaryGuardian(guardianId: string, studentId: string): Promise<void> {
    try {
      console.log('Setting primary guardian', { guardianId, studentId });

      await this.guardianRepository.setPrimaryGuardian(guardianId, studentId);

      console.log('Primary guardian set successfully', { guardianId, studentId });
    } catch (error) {
      console.error('Error setting primary guardian', { error, guardianId, studentId });
      throw ErrorHandler.handle(error, 'Set primary guardian');
    }
  }

  /**
   * Validate roll number uniqueness
   * @param rollNumber - Roll number
   * @param classId - Class ID
   * @param sectionId - Section ID
   * @param academicYearId - Academic year ID
   * @param excludeStudentId - Student ID to exclude
   * @returns True if unique
   */
  async validateRollNumberUniqueness(
    rollNumber: string,
    classId: string,
    sectionId: string,
    academicYearId: string,
    excludeStudentId?: string
  ): Promise<boolean> {
    try {
      return await this.academicRecordRepository.isRollNumberUnique(
        rollNumber,
        classId,
        sectionId,
        academicYearId,
        excludeStudentId
      );
    } catch (error) {
      console.error('Error validating roll number uniqueness', { error, rollNumber });
      throw ErrorHandler.handle(error, 'Validate roll number uniqueness');
    }
  }

  /**
   * Check class capacity
   * @param classId - Class ID
   * @param sectionId - Section ID
   * @param academicYearId - Academic year ID
   * @returns Capacity information
   */
  async checkClassCapacity(
    classId: string,
    sectionId: string,
    academicYearId: string
  ): Promise<{ current: number; maximum: number; available: number }> {
    try {
      const capacity = await this.academicRecordRepository.getClassCapacity(
        classId,
        sectionId,
        academicYearId
      );

      return {
        ...capacity,
        available: capacity.maximum - capacity.current
      };
    } catch (error) {
      console.error('Error checking class capacity', { error, classId, sectionId });
      throw ErrorHandler.handle(error, 'Check class capacity');
    }
  }

  /**
   * Transfer students to new class/section
   * @param studentIds - Array of student IDs
   * @param newClassId - New class ID
   * @param newSectionId - New section ID
   */
  async transferStudents(
    studentIds: string[],
    newClassId: string,
    newSectionId: string
  ): Promise<void> {
    try {
      console.log('Transferring students', {
        studentIds,
        newClassId,
        newSectionId,
        count: studentIds.length
      });

      // Update academic records for all students
      for (const studentId of studentIds) {
        const academicRecord = await this.academicRecordRepository.findByStudentId(studentId);
        if (academicRecord) {
          await this.academicRecordRepository.update(academicRecord.id, {
            class_id: newClassId,
            section_id: newSectionId
          });
        }

        // Also update student record
        await this.studentRepository.update(studentId, {
          class_id: newClassId,
          section_id: newSectionId
        });
      }

      console.log('Students transferred successfully', {
        count: studentIds.length,
        newClassId,
        newSectionId
      });
    } catch (error) {
      console.error('Error transferring students', { error, studentIds });
      throw ErrorHandler.handle(error, 'Transfer students');
    }
  }

  /**
   * Promote students to new academic year
   * @param classId - Current class ID
   * @param sectionId - Current section ID
   * @param currentAcademicYearId - Current academic year ID
   * @param newAcademicYearId - New academic year ID
   */
  async promoteStudents(
    classId: string,
    sectionId: string,
    currentAcademicYearId: string,
    newAcademicYearId: string
  ): Promise<void> {
    try {
      console.log('Promoting students', { 
        classId, 
        sectionId, 
        currentAcademicYearId, 
        newAcademicYearId 
      });

      const academicRecords = await this.academicRecordRepository.findByClassSection(
        classId,
        sectionId,
        currentAcademicYearId
      );

      // Update academic year for all records
      for (const record of academicRecords.data) {
        await this.academicRecordRepository.update(record.id, {
          academic_year_id: newAcademicYearId
        });

        // Also update student record
        await this.studentRepository.update(record.student_id, {
          academic_year_id: newAcademicYearId
        });
      }

      console.log('Students promoted successfully', { 
        count: academicRecords.data.length,
        newAcademicYearId 
      });
    } catch (error) {
      console.error('Error promoting students', { error, classId, sectionId });
      throw ErrorHandler.handle(error, 'Promote students');
    }
  }

  /**
   * Private method to validate student creation data
   */
  private async validateStudentCreation(data: CreateStudentData): Promise<void> {
    // Check roll number uniqueness
    const isRollNumberUnique = await this.academicRecordRepository.isRollNumberUnique(
      data.student.roll_number,
      data.student.class_id,
      data.student.section_id,
      data.student.academic_year_id
    );

    if (!isRollNumberUnique) {
      throw new ServiceError(
        ErrorCode.ROLL_NUMBER_TAKEN,
        'Roll number already exists in this class and section',
        null,
        'Validate student creation'
      );
    }

    // Check class capacity
    const capacity = await this.academicRecordRepository.getClassCapacity(
      data.student.class_id,
      data.student.section_id,
      data.student.academic_year_id
    );

    if (capacity.current >= capacity.maximum) {
      throw new ServiceError(
        ErrorCode.ENROLLMENT_LIMIT_EXCEEDED,
        'Class section is at full capacity',
        null,
        'Validate student creation'
      );
    }

    // Check email uniqueness if provided
    if (data.student.email) {
      const isEmailUnique = await this.studentRepository.isEmailUnique(data.student.email);
      if (!isEmailUnique) {
        throw new ServiceError(
          ErrorCode.DUPLICATE_VALUE,
          'Email address already exists',
          null,
          'Validate student creation'
        );
      }
    }
  }
}
