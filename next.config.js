/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Turbopack is enabled by default in Next.js 15 when running `next dev --turbo`
  // No explicit configuration needed here for Turbopack for development.
  // For production builds, Turbopack is not yet stable, Next.js will use Webpack.

  // ESLint configuration
  eslint: {
    // Only run ESLint on specific directories during build
    dirs: ['src'],
    // Allow production builds to complete even if there are ESLint warnings
    ignoreDuringBuilds: false,
  },

  // TypeScript configuration
  typescript: {
    // Allow production builds to complete even if there are TypeScript errors
    ignoreBuildErrors: false,
  },
};

module.exports = nextConfig;