// src/services/enrollmentManagementService.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database';
import { <PERSON>rror<PERSON><PERSON>, ErrorHandler, ServiceError } from './core/errorHandler';

// Service imports
import { ValidationService } from '../services/enrollment/validationService';
import { CompleteStudentData, CreateStudentData, StudentManagementService } from './studentManagementService';

// Repository imports
import { DocumentRepository, IDocumentRepository } from '../repositories/documentRepository';

// Type imports
import {
    AcademicRecordInsert,
    DATABASE_CONFIG,
    DocumentInsert,
    GuardianInsert,
    StudentInsert
} from '../constants/database';

/**
 * Enrollment step enumeration
 */
export enum EnrollmentStep {
  PERSONAL_INFO = 'personal_info',
  GUARDIAN_DETAILS = 'guardian_details',
  ACADEMIC_INFO = 'academic_info',
  DOCUMENTS = 'documents',
  REVIEW = 'review',
  COMPLETED = 'completed'
}

/**
 * Enrollment session interface
 */
export interface EnrollmentSession {
  id: string;
  currentStep: EnrollmentStep;
  status: string;
  data: EnrollmentData;
  validationErrors: Record<string, string[]>;
  createdAt: string;
  updatedAt: string;
  expiresAt: string;
}

/**
 * Complete enrollment data interface
 */
export interface EnrollmentData {
  student?: Partial<StudentInsert>;
  guardian?: Partial<GuardianInsert>;
  academic?: Partial<AcademicRecordInsert>;
  documents?: DocumentFile[];
}

/**
 * Document file interface
 */
export interface DocumentFile {
  type: string;
  file: File;
  isRequired: boolean;
}

/**
 * Enrollment progress interface
 */
export interface EnrollmentProgress {
  currentStep: EnrollmentStep;
  completedSteps: EnrollmentStep[];
  totalSteps: number;
  percentage: number;
  isComplete: boolean;
}

/**
 * Enrollment result interface
 */
export interface EnrollmentResult {
  success: boolean;
  studentData?: CompleteStudentData;
  errors?: string[];
  warnings?: string[];
}

/**
 * Enrollment management service interface
 */
export interface IEnrollmentManagementService {
  // Session management
  startEnrollment(): Promise<EnrollmentSession>;
  resumeEnrollment(sessionId: string): Promise<EnrollmentSession>;
  updateEnrollmentStep(sessionId: string, step: EnrollmentStep, stepData: any): Promise<EnrollmentSession>;
  getEnrollmentProgress(sessionId: string): Promise<EnrollmentProgress>;
  
  // Validation
  validateStep(step: EnrollmentStep, data: any): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }>;
  validateCompleteEnrollment(data: EnrollmentData): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }>;
  
  // Completion
  completeEnrollment(sessionId: string): Promise<EnrollmentResult>;
  cancelEnrollment(sessionId: string): Promise<void>;
  
  // Utilities
  getSessionById(sessionId: string): Promise<EnrollmentSession | null>;
  cleanupExpiredSessions(): Promise<string[]>;
}

/**
 * Enrollment management service implementation
 * Orchestrates the complete enrollment process with validation and session management
 */
export class EnrollmentManagementService implements IEnrollmentManagementService {
  private studentManagementService: StudentManagementService;
  private validationService: ValidationService;
  private documentRepository: IDocumentRepository;
  private client: SupabaseClient<Database>;

  // In-memory session storage (in production, use Redis or database)
  private sessions: Map<string, EnrollmentSession> = new Map();

  constructor(client: SupabaseClient<Database>) {
    this.client = client;
    this.studentManagementService = new StudentManagementService(client);
    this.validationService = new ValidationService(client);
    this.documentRepository = new DocumentRepository(client);
  }

  /**
   * Start a new enrollment session
   * @returns New enrollment session
   */
  async startEnrollment(): Promise<EnrollmentSession> {
    try {
      console.log('Starting new enrollment session');

      const sessionId = this.generateSessionId();
      const now = new Date();
      const expiresAt = new Date(now.getTime() + DATABASE_CONFIG.SESSION_TIMEOUT);

      const session: EnrollmentSession = {
        id: sessionId,
        currentStep: EnrollmentStep.PERSONAL_INFO,
        status: DATABASE_CONFIG.ENROLLMENT_STATUS.DRAFT,
        data: {},
        validationErrors: {},
        createdAt: now.toISOString(),
        updatedAt: now.toISOString(),
        expiresAt: expiresAt.toISOString()
      };

      this.sessions.set(sessionId, session);

      console.log('Enrollment session started', { sessionId });
      return session;
    } catch (error) {
      console.error('Error starting enrollment session', { error });
      throw ErrorHandler.handle(error, 'Start enrollment');
    }
  }

  /**
   * Resume an existing enrollment session
   * @param sessionId - Session ID
   * @returns Enrollment session
   */
  async resumeEnrollment(sessionId: string): Promise<EnrollmentSession> {
    try {
      console.log('Resuming enrollment session', { sessionId });

      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new ServiceError(
          ErrorCode.SESSION_NOT_FOUND,
          'Enrollment session not found',
          null,
          'Resume enrollment'
        );
      }

      // Check if session has expired
      if (new Date() > new Date(session.expiresAt)) {
        this.sessions.delete(sessionId);
        throw new ServiceError(
          ErrorCode.SESSION_EXPIRED,
          'Enrollment session has expired',
          null,
          'Resume enrollment'
        );
      }

      console.log('Enrollment session resumed', { sessionId, currentStep: session.currentStep });
      return session;
    } catch (error) {
      console.error('Error resuming enrollment session', { error, sessionId });
      throw ErrorHandler.handle(error, 'Resume enrollment');
    }
  }

  /**
   * Update enrollment step data
   * @param sessionId - Session ID
   * @param step - Enrollment step
   * @param stepData - Step data
   * @returns Updated session
   */
  async updateEnrollmentStep(
    sessionId: string,
    step: EnrollmentStep,
    stepData: any
  ): Promise<EnrollmentSession> {
    try {
      console.log('Updating enrollment step', { sessionId, step });

      const session = await this.resumeEnrollment(sessionId);

      // Validate step data
      const validation = await this.validateStep(step, stepData);
      if (!validation.isValid) {
        session.validationErrors[step] = validation.errors;
        this.sessions.set(sessionId, session);
        
        throw new ServiceError(
          ErrorCode.VALIDATION_ERROR,
          'Step validation failed',
          validation.errors,
          'Update enrollment step'
        );
      }

      // Update session data
      switch (step) {
        case EnrollmentStep.PERSONAL_INFO:
          session.data.student = stepData;
          break;
        case EnrollmentStep.GUARDIAN_DETAILS:
          session.data.guardian = stepData;
          break;
        case EnrollmentStep.ACADEMIC_INFO:
          session.data.academic = stepData;
          break;
        case EnrollmentStep.DOCUMENTS:
          session.data.documents = stepData;
          break;
      }

      // Clear validation errors for this step
      delete session.validationErrors[step];

      // Update session metadata
      session.currentStep = this.getNextStep(step);
      session.status = DATABASE_CONFIG.ENROLLMENT_STATUS.IN_PROGRESS;
      session.updatedAt = new Date().toISOString();

      this.sessions.set(sessionId, session);

      console.log('Enrollment step updated', { 
        sessionId, 
        step, 
        nextStep: session.currentStep 
      });

      return session;
    } catch (error) {
      console.error('Error updating enrollment step', { error, sessionId, step });
      throw ErrorHandler.handle(error, 'Update enrollment step');
    }
  }

  /**
   * Get enrollment progress
   * @param sessionId - Session ID
   * @returns Enrollment progress
   */
  async getEnrollmentProgress(sessionId: string): Promise<EnrollmentProgress> {
    try {
      const session = await this.resumeEnrollment(sessionId);

      const allSteps = [
        EnrollmentStep.PERSONAL_INFO,
        EnrollmentStep.GUARDIAN_DETAILS,
        EnrollmentStep.ACADEMIC_INFO,
        EnrollmentStep.DOCUMENTS
      ];

      const completedSteps = this.getCompletedSteps(session);
      const percentage = Math.round((completedSteps.length / allSteps.length) * 100);

      return {
        currentStep: session.currentStep,
        completedSteps,
        totalSteps: allSteps.length,
        percentage,
        isComplete: session.currentStep === EnrollmentStep.COMPLETED
      };
    } catch (error) {
      console.error('Error getting enrollment progress', { error, sessionId });
      throw ErrorHandler.handle(error, 'Get enrollment progress');
    }
  }

  /**
   * Validate enrollment step
   * @param step - Enrollment step
   * @param data - Step data
   * @returns Validation result
   */
  async validateStep(
    step: EnrollmentStep,
    data: any
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    try {
      return await this.validationService.validateStep(step, data);
    } catch (error) {
      console.error('Error validating step', { error, step });
      throw ErrorHandler.handle(error, 'Validate step');
    }
  }

  /**
   * Validate complete enrollment data
   * @param data - Complete enrollment data
   * @returns Validation result
   */
  async validateCompleteEnrollment(
    data: EnrollmentData
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    try {
      return await this.validationService.validateCompleteEnrollment(data);
    } catch (error) {
      console.error('Error validating complete enrollment', { error });
      throw ErrorHandler.handle(error, 'Validate complete enrollment');
    }
  }

  /**
   * Complete enrollment process
   * @param sessionId - Session ID
   * @returns Enrollment result
   */
  async completeEnrollment(sessionId: string): Promise<EnrollmentResult> {
    try {
      console.log('Completing enrollment', { sessionId });

      const session = await this.resumeEnrollment(sessionId);

      // Validate complete enrollment data
      const validation = await this.validateCompleteEnrollment(session.data);
      if (!validation.isValid) {
        return {
          success: false,
          errors: validation.errors,
          warnings: validation.warnings
        };
      }

      // Create complete student record
      const createData: CreateStudentData = {
        student: session.data.student as StudentInsert,
        guardian: session.data.guardian as GuardianInsert,
        academicRecord: session.data.academic as AcademicRecordInsert
      };

      const studentData = await this.studentManagementService.createCompleteStudent(createData);

      // Upload documents if any
      if (session.data.documents && session.data.documents.length > 0) {
        await this.uploadDocuments(studentData.student.id, session.data.documents);
      }

      // Update session status
      session.status = DATABASE_CONFIG.ENROLLMENT_STATUS.COMPLETED;
      session.currentStep = EnrollmentStep.COMPLETED;
      session.updatedAt = new Date().toISOString();
      this.sessions.set(sessionId, session);

      console.log('Enrollment completed successfully', { 
        sessionId, 
        studentId: studentData.student.id 
      });

      return {
        success: true,
        studentData,
        warnings: validation.warnings
      };
    } catch (error) {
      console.error('Error completing enrollment', { error, sessionId });
      
      // Update session with error status
      const session = this.sessions.get(sessionId);
      if (session) {
        session.status = DATABASE_CONFIG.ENROLLMENT_STATUS.REJECTED;
        session.updatedAt = new Date().toISOString();
        this.sessions.set(sessionId, session);
      }

      return {
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      };
    }
  }

  /**
   * Cancel enrollment session
   * @param sessionId - Session ID
   */
  async cancelEnrollment(sessionId: string): Promise<void> {
    try {
      console.log('Cancelling enrollment', { sessionId });

      this.sessions.delete(sessionId);

      console.log('Enrollment cancelled', { sessionId });
    } catch (error) {
      console.error('Error cancelling enrollment', { error, sessionId });
      throw ErrorHandler.handle(error, 'Cancel enrollment');
    }
  }

  /**
   * Get session by ID
   * @param sessionId - Session ID
   * @returns Enrollment session or null
   */
  async getSessionById(sessionId: string): Promise<EnrollmentSession | null> {
    try {
      const session = this.sessions.get(sessionId);
      
      if (session && new Date() > new Date(session.expiresAt)) {
        this.sessions.delete(sessionId);
        return null;
      }

      return session || null;
    } catch (error) {
      console.error('Error getting session by ID', { error, sessionId });
      throw ErrorHandler.handle(error, 'Get session by ID');
    }
  }

  /**
   * Clean up expired sessions
   * @returns Array of cleaned up session IDs
   */
  async cleanupExpiredSessions(): Promise<string[]> {
    try {
      console.log('Cleaning up expired sessions');

      const now = new Date();
      const expiredSessionIds: string[] = [];

      for (const [sessionId, session] of this.sessions.entries()) {
        if (now > new Date(session.expiresAt)) {
          this.sessions.delete(sessionId);
          expiredSessionIds.push(sessionId);
        }
      }

      console.log('Expired sessions cleaned up', { count: expiredSessionIds.length });
      return expiredSessionIds;
    } catch (error) {
      console.error('Error cleaning up expired sessions', { error });
      throw ErrorHandler.handle(error, 'Cleanup expired sessions');
    }
  }

  /**
   * Private helper methods
   */

  private generateSessionId(): string {
    return `enroll_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getNextStep(currentStep: EnrollmentStep): EnrollmentStep {
    const stepOrder = [
      EnrollmentStep.PERSONAL_INFO,
      EnrollmentStep.GUARDIAN_DETAILS,
      EnrollmentStep.ACADEMIC_INFO,
      EnrollmentStep.DOCUMENTS,
      EnrollmentStep.REVIEW,
      EnrollmentStep.COMPLETED
    ];

    const currentIndex = stepOrder.indexOf(currentStep);
    return stepOrder[currentIndex + 1] || EnrollmentStep.COMPLETED;
  }

  private getCompletedSteps(session: EnrollmentSession): EnrollmentStep[] {
    const completed: EnrollmentStep[] = [];

    if (session.data.student) completed.push(EnrollmentStep.PERSONAL_INFO);
    if (session.data.guardian) completed.push(EnrollmentStep.GUARDIAN_DETAILS);
    if (session.data.academic) completed.push(EnrollmentStep.ACADEMIC_INFO);
    if (session.data.documents) completed.push(EnrollmentStep.DOCUMENTS);

    return completed;
  }

  private async uploadDocuments(studentId: string, documents: DocumentFile[]): Promise<void> {
    for (const doc of documents) {
      // In a real implementation, you would upload the file to storage first
      // and then create the document record
      const documentData: DocumentInsert = {
        student_id: studentId,
        type: doc.type,
        file_name: `${studentId}_${doc.type}_${Date.now()}`,
        original_name: doc.file.name,
        file_path: `/documents/${studentId}/${doc.file.name}`,
        file_url: `/documents/${studentId}/${doc.file.name}`,
        file_size: doc.file.size,
        mime_type: doc.file.type,
        uploaded_at: new Date().toISOString(),
        is_required: doc.isRequired,
        status: DATABASE_CONFIG.DOCUMENT_STATUS.PENDING,
        is_active: true
      };

      await this.documentRepository.create(documentData);
    }
  }
}
