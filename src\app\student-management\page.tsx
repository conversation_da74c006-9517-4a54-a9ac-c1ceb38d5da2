// src/app/student-management/page.tsx
'use client';

import { useState } from 'react';
import StudentManagement from './_components/student-management';

export default function StudentManagementPage() {
  const [activeSubSection, setActiveSubSection] = useState('Current Students');

  return (
    <StudentManagement 
      activeSubSection={activeSubSection}
      onSubSectionChange={setActiveSubSection}
    />
  );
}
